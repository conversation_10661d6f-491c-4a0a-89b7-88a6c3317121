"use client";

import { Button } from "@relume_io/relume-ui";
import React from "react";

export function Header145() {
  return (
    <section id="relume">
      <div className="px-[5%] py-16 md:py-24 lg:py-28">
        <div className="container max-w-lg">
          <div className="flex w-full flex-col items-center text-center">
            <h1 className="mb-5 text-6xl font-bold md:mb-6 md:text-9xl lg:text-10xl">
              Magazines & rapporten die écht gelezen worden
            </h1>
            <p className="md:text-md">
              Magazines en (duurzaamheids)rapporten zijn dankbare tools voor je
              communicatie. Zelfs in een digitale wereld, want ze maken de zaken
              soms net iets tastbaarder. Maar belangrijk, of het nu online of
              offline is; wij maken content die effectief gelezen wordt. Dat
              lijkt logisch, maar dat is het niet altijd. Jouw feiten, data, en
              andere zaken brengen we op een verhalende manier via de juiste
              concepten. En, op een journalistieke manier, als het even kan. We
              creëren jouw content van A tot Z, vanaf research of concept tot
              opmaak.  The proof of the pudding is in the eating, right? Daarom
              geven we hieronder enkele voorbeelden uit eigen hand.
            </p>
            <div className="mt-6 flex items-center justify-center gap-x-4 md:mt-8">
              <Button title="Ontdek meer">Ontdek meer</Button>
              <Button title="Contacteer ons" variant="secondary">
                Contacteer ons
              </Button>
            </div>
          </div>
        </div>
      </div>
      <div>
        <img
          src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image.svg"
          className="aspect-video size-full object-cover"
          alt="Relume placeholder image"
        />
      </div>
    </section>
  );
}
