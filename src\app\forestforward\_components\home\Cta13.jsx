"use client";

import { Button } from "@relume_io/relume-ui";
import React from "react";

export function Cta13() {
  return (
    <section id="relume" className="relative px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container grid grid-rows-1 items-start gap-y-5 md:grid-cols-2 md:gap-x-12 md:gap-y-8 lg:gap-x-20 lg:gap-y-16">
        <div>
          <h1 className="text-5xl font-bold md:text-7xl lg:text-8xl">
            Nood aan boscompensatie?
          </h1>
        </div>
        <div>
          <p className="md:text-md">
            We zijn het erover eens dat bossen heel belangrijk zijn. Maar wie
            toch moet ontbossen op zijn eigendom, heeft de mogelijkheid en/of
            verplichting om bos in natura te compenseren. Dat is voor iedereen
            de beste oplossing, want dan weten we tenminste dat er elders (een
            veelvoud) natuur in de plaats komt. Maar evident is het niet.
            <PERSON><PERSON><PERSON>, als je er niet in thuis bent. <PERSON><PERSON> je hierin begeleiding?
            Forest Forward vindt de nodige gronden en zorgt voor een doordachte,
            ecologisch waardevolle en juridisch sluitende invulling van jouw
            compensatieverplichting.
          </p>
          <div className="mt-6 flex flex-wrap gap-4 md:mt-8">
            <Button title="Lees meer">Lees meer</Button>
            <Button title="Contacteer ons" variant="secondary">
              Contacteer ons
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
