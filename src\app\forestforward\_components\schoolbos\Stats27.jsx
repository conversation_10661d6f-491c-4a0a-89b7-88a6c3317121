"use client";

import {  } from "@relume_io/relume-ui";
import React from "react";

export function Stats27() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="mb-12 max-w-lg md:mb-18 lg:mb-20">
          <h2 className="mb-5 text-5xl font-bold md:mb-6 md:text-7xl lg:text-8xl">
            Wat zegt de wetenschap?
          </h2>
        </div>
        <div className="grid grid-cols-1 gap-y-8 lg:grid-cols-3 lg:gap-x-8 lg:gap-y-12">
          <div className="p-8">
            <h3 className="text-md leading-[1.4] font-bold md:text-xl">
              Milieubewuster
            </h3>
            <p className="mt-2">
              Kinderen die opgroeien met natuurbeleving ontwikkelen een sterkere
              band met het milieu en vertonen later vaker milieubewust gedrag.
            </p>
          </div>
          <div className="p-8">
            <h3 className="text-md leading-[1.4] font-bold md:text-xl">
              Gezondheid
            </h3>
            <p className="mt-2">
              Leerlingen op “groene” scholen scoren beter op concentratietesten
              en vertonen minder stress en mentale gezondheidsproblemen.
            </p>
          </div>
          <div className="p-8">
            <h3 className="text-md leading-[1.4] font-bold md:text-xl">
              Sociaal
            </h3>
            <p className="mt-2">
              Groen in de omgeving stimuleert hogere cognitieve functies,
              sociale vaardigheden en creativiteit.
            </p>
          </div>
        </div>
        <div className="mt-10 flex flex-wrap items-center gap-4 md:mt-14 lg:mt-16" />
      </div>
    </section>
  );
}
