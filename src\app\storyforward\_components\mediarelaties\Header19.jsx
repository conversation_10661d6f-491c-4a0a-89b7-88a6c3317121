"use client";

import React from "react";

export function Header19() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="grid grid-cols-1 gap-x-20 gap-y-12 md:gap-y-16 lg:grid-cols-2 lg:items-center">
          <div>
            <img
              src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image.svg"
              className="w-full rounded-image object-cover"
              alt="Relume placeholder image"
            />
          </div>
          <div>
            <h1 className="mb-5 text-6xl font-bold md:mb-6 md:text-9xl lg:text-10xl">
              Mediarelaties
            </h1>
            <p className="md:text-md">
              We hebben een neus voor nieuwswaardige verhalen en weten wat
              journalisten willen (en wat niet). He<PERSON> wat van onze experten
              hebben niet toevallig een verleden als journalist. Ze weten dus
              hoe het werkt. We pitchen jouw verhaal en jouw experten bij de
              media en zorgen voor een langetermijnrelatie met journalisten die
              voor jou relevant zijn.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
