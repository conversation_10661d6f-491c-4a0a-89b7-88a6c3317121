"use client";

import React from "react";

export function Header1() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="grid grid-cols-1 gap-x-20 gap-y-12 md:gap-y-16 lg:grid-cols-2 lg:items-center">
          <div>
            <h1 className="mb-5 text-6xl font-bold md:mb-6 md:text-9xl lg:text-10xl">
              TO DELETE
            </h1>
            <p className="md:text-md">
              A<PERSON>rader is zeker onze workshop ‘Verantwoord communiceren over
              duurzaamheid’. Want wat we merken, is dat meer en meer bedrijven
              actief geëngageerd zijn, maar aarzelen om erover te communiceren
              uit vrees om mogelijk ‘verdacht’ te worden van greenwashing – ook
              al is het onterecht. Hoe ga je daar als bedrijf mee om? Hoe buig
              je een risico om in een opportuniteit? Die vragen beantwoorden wij
              tijdens onze workshop. We geven je de nodige inzichten, leren je
              de risico’s te herkennen en reiken je de nodige tools en best
              practices aan om vertrouwen op te bouwen en je duurzaam engagement
              wél in de kijker te zetten.
            </p>
          </div>
          <div>
            <img
              src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image.svg"
              className="w-full rounded-image object-cover"
              alt="Relume placeholder image"
            />
          </div>
        </div>
      </div>
    </section>
  );
}
