"use client";

import React from "react";
import { ServiceBlock } from "./ServiceBlock";

export function ServiceGrid({ services }) {
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 md:gap-6 auto-rows-[200px] md:auto-rows-[250px]">
      {services.map((service) => (
        <ServiceBlock
          key={service.id}
          title={service.title}
          description={service.description}
          href={service.href}
          size={service.size}
          icon={service.icon}
        />
      ))}
    </div>
  );
}
