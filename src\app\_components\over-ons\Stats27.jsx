"use client";

// import {  } from "@relume_io/relume-ui";
import React from "react";

export function Stats27() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="mb-12 max-w-lg md:mb-18 lg:mb-20">
          <h2 className="mb-5 text-5xl font-bold md:mb-6 md:text-7xl lg:text-8xl">
            Ons ecosysteem
          </h2>
          <p className="md:text-md">
            Samen vormen ze een ecosysteem dat elkaar constant versterkt en één
            gezamenlijke missie deelt: jou helpen om op een duurzame manier
            maatschappelijke impact te realiseren.
          </p>
        </div>
        <div className="grid grid-cols-1 gap-y-8 lg:grid-cols-3 lg:gap-x-8 lg:gap-y-12">
          <div className="p-8">
            <p className="mb-8 text-10xl leading-[1.3] font-bold md:mb-10 md:text-[4rem] lg:mb-12 lg:text-[5rem]">
              LOGO
            </p>
            <h3 className="text-md leading-[1.4] font-bold md:text-xl">Doen</h3>
            <p className="mt-2">
              Met Forest Forward helpen we je om lokale natuur te creëren en
              biodiversiteit te versterken. Op zo’n manier dat het voor jouw
              bedrijf een meerwaarde vormt.
            </p>
          </div>
          <div className="p-8">
            <p className="mb-8 text-10xl leading-[1.3] font-bold md:mb-10 md:text-[4rem] lg:mb-12 lg:text-[5rem]">
              LOGO
            </p>
            <h3 className="text-md leading-[1.4] font-bold md:text-xl">
              Inspireer
            </h3>
            <p className="mt-2">
              Story Forward brengt jouw maatschappelijke en duurzame
              initiatieven tot leven via storytelling en strategische
              communicatie. We zorgen ervoor dat je op een slimme en
              geloofwaardige manier anderen kan inspireren.
            </p>
          </div>
          <div className="p-8">
            <p className="mb-8 text-10xl leading-[1.3] font-bold md:mb-10 md:text-[4rem] lg:mb-12 lg:text-[5rem]">
              LOGO
            </p>
            <h3 className="text-md leading-[1.4] font-bold md:text-xl">
              Connecteer
            </h3>
            <p className="mt-2">
              Met Lagom zorgen we dat je jouw stakeholders impactvol kan
              verbinden. Van teambuildings over workshops tot bedrijfsdagen:
              jouw mensen moeten zich meer dan ooit betrokken voelen, dat is
              onze missie.
            </p>
          </div>
        </div>
        <div className="mt-10 flex flex-wrap items-center gap-4 md:mt-14 lg:mt-16" />
      </div>
    </section>
  );
}
