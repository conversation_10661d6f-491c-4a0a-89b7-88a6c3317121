# Multi-Theme Color System Guide

This guide explains how to use the multi-theme color system implemented for your website.

## Overview

The theme system allows different sections of your website (homepage, forestforward, lagom, storyforward) to have distinct color schemes while maintaining consistent design elements like spacing, typography, shadows, and borders.

## How It Works

### 1. CSS Custom Properties
Each theme defines CSS custom properties (CSS variables) for colors:
- `--color-background` (and variants)
- `--color-border` (and variants) 
- `--color-text` (and variants)
- `--color-link` (and variants)

### 2. Tailwind Configuration
Your `tailwind.config.js` maps these CSS variables to Tailwind classes:
```javascript
colors: {
  background: {
    DEFAULT: "var(--color-background)",
    primary: "var(--color-background-primary)",
    // ... more variants
  },
  // ... other color categories
}
```

### 3. Theme Classes
Each theme has a CSS class that sets the color variables:
- `.theme-homepage` - Default blue theme
- `.theme-forestforward` - Green nature theme  
- `.theme-lagom` - Warm orange theme
- `.theme-storyforward` - Creative purple theme

## File Structure

```
src/app/
├── themes/
│   ├── homepage.css
│   ├── forestforward.css
│   ├── lagom.css
│   └── storyforward.css
├── utils/
│   └── theme.js
├── forestforward/
│   └── layout.js (applies theme-forestforward)
├── lagom/
│   └── layout.js (applies theme-lagom)
├── storyforward/
│   └── layout.js (applies theme-storyforward)
└── globals.css (imports all themes)
```

## Using the Theme System

### 1. In Components
Use theme-aware Tailwind classes instead of hardcoded colors:

**❌ Before (hardcoded colors):**
```jsx
<button className="bg-blue-500 text-white border-blue-600">
  Click me
</button>
```

**✅ After (theme-aware):**
```jsx
<button className="bg-link text-text-alternative border-border-primary">
  Click me
</button>
```

### 2. Available Theme Classes

#### Background Colors
- `bg-background` - Main background
- `bg-background-primary` - Light background variant
- `bg-background-secondary` - Medium background variant  
- `bg-background-tertiary` - Darker background variant
- `bg-background-alternative` - Dark/contrasting background

#### Text Colors
- `text-text` - Default text color
- `text-text-primary` - Primary text (usually darker)
- `text-text-secondary` - Secondary text (usually lighter)
- `text-text-alternative` - Alternative text (for dark backgrounds)

#### Border Colors
- `border-border` - Default border
- `border-border-primary` - Primary border
- `border-border-secondary` - Secondary border
- `border-border-tertiary` - Tertiary border
- `border-border-alternative` - Alternative border

#### Link Colors
- `text-link` - Default link color
- `text-link-primary` - Primary link color
- `text-link-secondary` - Secondary link color
- `text-link-alternative` - Alternative link color

### 3. Component Examples

See the example components in `src/app/_components/examples/`:
- `ThemedButton.jsx` - Button with theme-aware colors
- `ThemedCard.jsx` - Card component with theme variants
- `ThemedNavigation.jsx` - Navigation with theme colors

## Adding New Themes

1. Create a new CSS file in `src/app/themes/`:
```css
/* src/app/themes/newtheme.css */
.theme-newtheme {
  --color-background: #ffffff;
  --color-background-primary: #f8fafc;
  /* ... define all color variables */
}
```

2. Import it in `globals.css`:
```css
@import '../styles/newtheme.css';
```

3. Add to theme utility:
```javascript
// src/app/utils/theme.js
export const THEMES = {
  // ... existing themes
  NEWTHEME: 'theme-newtheme',
};
```

4. Create layout file if needed:
```jsx
// src/app/newsection/layout.js
export default function NewSectionLayout({ children }) {
  return (
    <div className="theme-newtheme">
      {children}
    </div>
  );
}
```

## Customizing Colors

To change colors for a theme, edit the corresponding CSS file in `src/app/themes/`. 

For example, to make Forest Forward more blue-green:
```css
/* src/app/themes/forestforward.css */
.theme-forestforward {
  --color-link: #0891b2; /* cyan-600 instead of green */
  --color-link-primary: #0e7490; /* cyan-700 */
  /* ... other color adjustments */
}
```

## Testing Themes

Visit `/theme-demo` to see all themes in action and test your changes.

## Best Practices

1. **Always use theme classes** instead of hardcoded Tailwind colors for anything that should change between themes
2. **Keep static colors static** - Use hardcoded colors for things like success/error states that shouldn't change
3. **Test all themes** when making changes to ensure consistency
4. **Use semantic naming** - The color variable names describe their purpose, not their appearance
5. **Maintain contrast** - Ensure sufficient contrast between text and background colors in all themes

## Migration Guide

To convert existing components to use the theme system:

1. Identify hardcoded color classes (e.g., `bg-blue-500`, `text-gray-800`)
2. Replace with appropriate theme classes (e.g., `bg-link`, `text-text-primary`)
3. Test the component in all themes to ensure it looks good
4. Use the demo page to verify your changes

## Static Colors (Never Change)

Some colors should remain consistent across all themes:
- `brand-black` / `brand-white` - Pure black/white
- `neutral-*` - Neutral grays
- `system-success-*` - Success states (green)
- `system-error-*` - Error states (red)

These are defined in your Tailwind config and don't use CSS variables.
