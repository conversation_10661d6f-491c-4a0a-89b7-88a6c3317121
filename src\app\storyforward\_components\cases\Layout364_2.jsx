"use client";

import { Button } from "@relume_io/relume-ui";
import React from "react";
import { RxChevronRight } from "react-icons/rx";

export function Layout364_2() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="mb-12 md:mb-18 lg:mb-20">
          <div className="mx-auto max-w-lg text-center">
            <p className="mb-3 font-semibold md:mb-4">House Of Finance</p>
            <h2 className="rb-5 mb-5 text-5xl font-bold md:mb-6 md:text-7xl lg:text-8xl">
              Private equity in beeld en woord
            </h2>
            <p className="md:text-md">
              Vermogensbegeleider House of Finance wil ondernemers onder meer
              financieel bijscholen. Daarvoor moeten ze moeilijke thema’s op een
              toegankelijke manier naar voren schuiven. En dat liefst op een
              heldere manier, veelvoudig, én efficiënt. Het thema dat we in deze
              moesten uitwerken: hoe als ondernemer om te gaan met private
              equity.
            </p>
          </div>
        </div>
        <div className="grid grid-cols-1 items-start gap-6 md:grid-cols-2 md:gap-8">
          <div className="p-6 md:p-8 lg:p-12">
            <div>
              <h3 className="mb-5 text-4xl leading-[1.2] font-bold md:mb-6 md:text-5xl lg:text-6xl">
                Onze aanpak
              </h3>
              <p>
                We bereidden een sterk inhoudelijk scenario voor en
                organiseerden een rondetafeldebat in de studio van één van onze
                nauwe partners. Aan de tafel: een viertal straffe experten in
                private equity. Zodra iedereen wist wat gedaan en gezegd -
                voorbereiding is in deze alles - trokken we de regiekamer in en
                leidden we het debat in goede banen.
              </p>
            </div>
            <div className="mt-6 flex flex-wrap gap-4 md:mt-8">
              <Button iconRight={<RxChevronRight />} variant="link" size="link">
                Contacteer ons
              </Button>
            </div>
          </div>
          <div className="p-6 md:p-8 lg:p-12">
            <div>
              <h3 className="mb-5 text-4xl leading-[1.2] font-bold md:mb-6 md:text-5xl lg:text-6xl">
                Het resultaat
              </h3>
              <p>
                Eén rondetafelopname zorgde voor diverse
                communicatie-opportuniteiten. Zo zorgde die ene opname voor een
                leerrijk videodebat, teasende snippers voor sociale media, een
                sterk interview in hun magazine, en het had zelfs tot een
                boeiende podcast kunnen leiden.
              </p>
            </div>
            <div className="mt-6 flex flex-wrap gap-4 md:mt-8">
              <Button iconRight={<RxChevronRight />} variant="link" size="link">
                Magazine Paneldebat
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
