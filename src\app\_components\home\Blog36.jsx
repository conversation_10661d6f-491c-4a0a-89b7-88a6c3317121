"use client";

import { <PERSON><PERSON>, <PERSON><PERSON> } from "@relume_io/relume-ui";
import React from "react";
import { RxChevronRight } from "react-icons/rx";

export function Blog36() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28 bg-white">
      <div className="container">
        <div className="mb-12 md:mb-18 lg:mb-20">
          <div className="mx-auto w-full max-w-lg text-center">
            <h2 className="rb-5 mb-5 text-5xl font-semibold md:mb-6 md:text-7xl lg:text-8xl">
              <PERSON><PERSON> onze blogs
            </h2>
          </div>
        </div>
        <div className="grid grid-cols-1 gap-x-8 gap-y-12 md:grid-cols-2 md:gap-y-16 lg:grid-cols-3">
          <div className="flex size-full flex-col items-center justify-start border border-border rounded-lg">
            <a href="#" className="w-full">
              <img
                src="/images/algemeen/moodboard 1.png"
                alt="Relume placeholder image"
                className="aspect-[3/2] size-full object-cover rounded-t-lg"
              />
            </a>
            <div className="px-5 py-6 md:p-6">
              <div className="rb-4 mb-4 flex w-full items-center justify-start">
                <Badge className="mr-4 bg-transparent border border-border rounded-md text-text-primary py-1 px-3">Insights</Badge>
                <p className="inline text-sm font-semibold">5 min read</p>
              </div>
              <a className="mb-2 block max-w-full" href="#">
                <h2 className="text-xl font-semibold md:text-2xl">
                  The Future of Sustainable Events
                </h2>
              </a>
              <p>
                Learn how sustainable practices are transforming the events
                industry.
              </p>
              <Button
                title="Read more"
                variant="link"
                size="link"
                iconRight={<RxChevronRight />}
                className="mt-6 flex items-center justify-center gap-x-2"
              >
                Read more
              </Button>
            </div>
          </div>
          <div className="flex size-full flex-col items-center justify-start rounded-lg border border-border">
            <a href="#" className="w-full">
              <img
                src="/images/algemeen/moodboard 2.png"
                alt="Relume placeholder image"
                className="aspect-[3/2] size-full object-cover rounded-t-lg"
              />
            </a>
            <div className="px-5 py-6 md:p-6">
              <div className="rb-4 mb-4 flex w-full items-center justify-start">
                <Badge className="mr-4 bg-transparent border border-border rounded-md text-text-primary py-1 px-3">Insights</Badge>
                <p className="inline text-sm font-semibold">5 min read</p>
              </div>
              <a className="mb-2 block max-w-full" href="#">
                <h2 className="text-xl font-semibold md:text-2xl">
                  Innovative Strategies for Eco-Friendly Branding
                </h2>
              </a>
              <p>
                Explore branding techniques that prioritize environmental
                sustainability.
              </p>
              <Button
                title="Read more"
                variant="link"
                size="link"
                iconRight={<RxChevronRight />}
                className="mt-6 flex items-center justify-center gap-x-2"
              >
                Read more
              </Button>
            </div>
          </div>
          <div className="flex size-full flex-col items-center justify-start border border-border rounded-lg">
            <a href="#" className="w-full">
              <img
                src="/images/algemeen/moodboard 3.png"
                alt="Relume placeholder image"
                className="aspect-[3/2] size-full object-cover rounded-t-lg"
              />
            </a>
            <div className="px-5 py-6 md:p-6">
              <div className="rb-4 mb-4 flex w-full items-center justify-start">
                <Badge className="mr-4 bg-transparent border border-border rounded-md text-text-primary py-1 px-3">Insights</Badge>
                <p className="inline text-sm font-semibold">5 min read</p>
              </div>
              <a className="mb-2 block max-w-full" href="#">
                <h2 className="text-xl font-semibold md:text-2xl">
                  Case Studies in Ecological Projects
                </h2>
              </a>
              <p>
                Discover successful projects that highlight ecological
                innovations.
              </p>
              <Button
                title="Read more"
                variant="link"
                size="link"
                iconRight={<RxChevronRight />}
                className="mt-6 flex items-center justify-center gap-x-2"
              >
                Read more
              </Button>
            </div>
          </div>
        </div>
        <div className="flex items-center justify-center">
          <Button
            title="View all"
            className="mt-10 md:mt-14 lg:mt-16 bg-link text-text-alternative hover:bg-link-primary rounded-lg"
          >
            Alle blogs bekijken
          </Button>
        </div>
      </div>
    </section>
  );
}
