/**
 * Theme utility functions for managing multi-theme color system
 */

// Available themes
export const THEMES = {
  HOMEPAGE: 'theme-homepage',
  FORESTFORWARD: 'theme-forestforward',
  LAGOM: 'theme-lagom',
  STORYFORWARD: 'theme-storyforward',
};

/**
 * Get theme class based on pathname
 * @param {string} pathname - Current pathname
 * @returns {string} Theme class name
 */
export function getThemeFromPath(pathname) {
  if (pathname.startsWith('/forestforward')) {
    return THEMES.FORESTFORWARD;
  }
  if (pathname.startsWith('/lagom')) {
    return THEMES.LAGOM;
  }
  if (pathname.startsWith('/storyforward')) {
    return THEMES.STORYFORWARD;
  }
  return THEMES.HOMEPAGE;
}

/**
 * Apply theme class to an element
 * @param {HTMLElement} element - Element to apply theme to
 * @param {string} theme - Theme class name
 */
export function applyTheme(element, theme) {
  // Remove all existing theme classes
  Object.values(THEMES).forEach(themeClass => {
    element.classList.remove(themeClass);
  });
  
  // Add the new theme class
  element.classList.add(theme);
}

/**
 * Get current theme from document body
 * @returns {string} Current theme class name
 */
export function getCurrentTheme() {
  const body = document.body;
  for (const theme of Object.values(THEMES)) {
    if (body.classList.contains(theme)) {
      return theme;
    }
  }
  return THEMES.HOMEPAGE;
}
