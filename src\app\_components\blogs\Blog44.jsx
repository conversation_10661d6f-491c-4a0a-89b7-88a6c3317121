"use client";

import { <PERSON><PERSON>, <PERSON><PERSON> } from "@relume_io/relume-ui";
import React from "react";
import { RxChevronRight } from "react-icons/rx";

export function Blog44() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="rb-12 mb-12 grid grid-cols-1 items-start justify-start gap-y-8 md:mb-18 md:grid-cols-[1fr_max-content] md:items-end md:justify-between md:gap-x-12 md:gap-y-4 lg:mb-20 lg:gap-x-20">
          <div className="w-full max-w-lg" />
          <div className="hidden flex-wrap items-center justify-end md:block">
            <Button title="View all" variant="secondary">
              View all
            </Button>
          </div>
        </div>
        <div className="grid grid-cols-1 gap-x-8 gap-y-12 md:grid-cols-2 md:gap-y-16 lg:grid-cols-3">
          <div>
            <a
              href="#"
              className="flex size-full flex-col items-center justify-start"
            >
              <div className="relative w-full overflow-hidden pt-[66%]">
                <img
                  src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image-landscape.svg"
                  alt="Relume placeholder image 1"
                  className="absolute inset-0 size-full object-cover"
                />
              </div>
              <div className="px-5 py-6 md:p-6">
                <div className="rb-4 mb-4 flex w-full items-center justify-start">
                  <Badge className="mr-4">Sustainability</Badge>
                  <p className="inline text-sm font-semibold">5 min read</p>
                </div>
                <a className="mb-2" href="#">
                  <h2 className="mb-2 text-xl font-bold md:text-2xl">
                    The Future of Eco-Friendly Practices
                  </h2>
                </a>
                <p>
                  Discover innovative strategies for sustainable living and
                  business.
                </p>
                <Button
                  title="Read more"
                  variant="link"
                  size="link"
                  iconRight={<RxChevronRight />}
                  className="mt-6 flex items-center gap-x-1"
                >
                  Read more
                </Button>
              </div>
            </a>
          </div>
          <div>
            <a
              href="#"
              className="flex size-full flex-col items-center justify-start"
            >
              <div className="relative w-full overflow-hidden pt-[66%]">
                <img
                  src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image-landscape.svg"
                  alt="Relume placeholder image 1"
                  className="absolute inset-0 size-full object-cover"
                />
              </div>
              <div className="px-5 py-6 md:p-6">
                <div className="rb-4 mb-4 flex w-full items-center justify-start">
                  <Badge className="mr-4">Events</Badge>
                  <p className="inline text-sm font-semibold">5 min read</p>
                </div>
                <a className="mb-2" href="#">
                  <h2 className="mb-2 text-xl font-bold md:text-2xl">
                    Creating Impactful Community Events
                  </h2>
                </a>
                <p>
                  Learn how to engage communities through meaningful events.
                </p>
                <Button
                  title="Read more"
                  variant="link"
                  size="link"
                  iconRight={<RxChevronRight />}
                  className="mt-6 flex items-center gap-x-1"
                >
                  Read more
                </Button>
              </div>
            </a>
          </div>
          <div>
            <a
              href="#"
              className="flex size-full flex-col items-center justify-start"
            >
              <div className="relative w-full overflow-hidden pt-[66%]">
                <img
                  src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image-landscape.svg"
                  alt="Relume placeholder image 1"
                  className="absolute inset-0 size-full object-cover"
                />
              </div>
              <div className="px-5 py-6 md:p-6">
                <div className="rb-4 mb-4 flex w-full items-center justify-start">
                  <Badge className="mr-4">Communication</Badge>
                  <p className="inline text-sm font-semibold">5 min read</p>
                </div>
                <a className="mb-2" href="#">
                  <h2 className="mb-2 text-xl font-bold md:text-2xl">
                    Mastering Strategic Communication
                  </h2>
                </a>
                <p>
                  Enhance your brand's voice with effective communication
                  strategies.
                </p>
                <Button
                  title="Read more"
                  variant="link"
                  size="link"
                  iconRight={<RxChevronRight />}
                  className="mt-6 flex items-center gap-x-1"
                >
                  Read more
                </Button>
              </div>
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
