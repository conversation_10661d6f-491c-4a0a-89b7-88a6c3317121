"use client";

import React, { useState, useRef } from "react";
import Link from "next/link";

export function ServiceBlock({ title, description, href, size, icon }) {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);
  const blockRef = useRef(null);

  const handleMouseMove = (e) => {
    if (blockRef.current) {
      const rect = blockRef.current.getBoundingClientRect();
      setMousePosition({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
    }
  };

  const handleMouseEnter = (e) => {
    setIsHovered(true);
    handleMouseMove(e);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  // Size classes for different grid layouts
  const sizeClasses = {
    small: "col-span-1 row-span-1", // 1x1
    wide: "col-span-2 row-span-1", // 2x1
    tall: "col-span-1 row-span-2", // 1x2
    large: "col-span-2 row-span-2", // 2x2
  };

  // Responsive size adjustments
  const responsiveSizeClasses = {
    small: "col-span-1 row-span-1 md:col-span-2 md:row-span-1 lg:col-span-1 lg:row-span-1",
    wide: "col-span-2 row-span-1 md:col-span-2 md:row-span-1 lg:col-span-2 lg:row-span-1", 
    tall: "col-span-1 row-span-2 md:col-span-2 md:row-span-1 lg:col-span-1 lg:row-span-2",
    large: "col-span-2 row-span-2 md:col-span-4 md:row-span-2 lg:col-span-2 lg:row-span-2",
  };

  return (
    <Link href={href} className="block">
      <div
        ref={blockRef}
        className={`
          ${responsiveSizeClasses[size]}
          relative overflow-hidden rounded-lg bg-background-primary border border-border
          transition-all duration-300 ease-out cursor-pointer group
          hover:shadow-lg hover:border-link hover:-translate-y-1
        `}
        onMouseMove={handleMouseMove}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {/* Circular hover effect */}
        <div
          className={`
            absolute rounded-full bg-link transition-all duration-500 ease-out pointer-events-none
            ${isHovered ? 'opacity-10' : 'opacity-0'}
          `}
          style={{
            width: isHovered ? '400px' : '0px',
            height: isHovered ? '400px' : '0px',
            left: mousePosition.x - 200,
            top: mousePosition.y - 200,
            transform: 'translate(0, 0)',
          }}
        />

        {/* Content */}
        <div className="relative z-10 p-4 md:p-6 h-full flex flex-col justify-between">
          {/* Icon */}
          <div className="flex justify-between items-start mb-3">
            <span className="text-2xl md:text-3xl">{icon}</span>
            <div className="w-6 h-6 rounded-full border-2 border-link opacity-60 group-hover:opacity-100 transition-opacity duration-300" />
          </div>

          {/* Text content */}
          <div className="flex-1 flex flex-col justify-end">
            <h3 className="text-lg md:text-xl lg:text-2xl font-semibold text-text-primary mb-2 group-hover:text-link transition-colors duration-300">
              {title}
            </h3>
            <p className="text-sm md:text-base text-text-secondary group-hover:text-text-primary transition-colors duration-300 overflow-hidden">
              <span className="block overflow-hidden text-ellipsis" style={{
                display: '-webkit-box',
                WebkitLineClamp: 3,
                WebkitBoxOrient: 'vertical'
              }}>
                {description}
              </span>
            </p>
          </div>

          {/* Arrow indicator */}
          <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0">
            <svg 
              className="w-5 h-5 text-link" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M7 17l9.2-9.2M17 17V7H7" 
              />
            </svg>
          </div>
        </div>
      </div>
    </Link>
  );
}
