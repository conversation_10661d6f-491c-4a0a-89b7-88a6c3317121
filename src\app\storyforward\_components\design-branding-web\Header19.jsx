"use client";

import React from "react";

export function Header19() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="grid grid-cols-1 gap-x-20 gap-y-12 md:gap-y-16 lg:grid-cols-2 lg:items-center">
          <div>
            <img
              src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image.svg"
              className="w-full rounded-image object-cover"
              alt="Relume placeholder image"
            />
          </div>
          <div>
            <h1 className="mb-5 text-6xl font-bold md:mb-6 md:text-9xl lg:text-10xl">
              Design, branding & web development
            </h1>
            <p className="md:text-md">
              <PERSON><PERSON> sterke, inhoudelijke boodschap is belangrijk, maar het oog wil
              in deze tijden ook wat. En terecht. Wil je jouw bedrijf of
              organisatie, jouw merk of product de juiste vorm geven? <PERSON>
              wisselen we graag samen met onze ervaren designers – elk met hun
              eigen skills – van gedachten. Want: weet dat de juiste design &
              branding je inhoudelijke verhaal mee kunnen maken of kraken. Zeker
              in deze ‘visuele’ tijden. Consistency is bovendien key. Een
              herkenbare, consistente stijl spreekt mensen niet alleen aan, het
              geeft jouw stakeholders ook vertrouwen. Samen ontwikkelen we
              daarom de juiste stijl en vertalen we die naar een huisstijlgids.
              Dat biedt jouw medewerkers duidelijke richtlijnen om makkelijk
              eenduidig te communiceren. Om jouw verhaal in de juiste woorden en
              beelden ook in een website te vertalen, hebben we onder leiding
              van Léon Missoul ook een jong, sterk en erg betrokken team van
              webdevelopers. We helpen je met het ontwikkelen van websites of
              landingspagina’s en geven je de tools om daarna zelf je site
              optimaal te beheren.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
