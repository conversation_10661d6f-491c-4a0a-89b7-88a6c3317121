.theme-forestforward {
  /* Background Colors */
  --color-background: #ffffff;
  --color-background-primary: #f4f6f8;
  --color-background-secondary: #dde4eb;
  --color-background-tertiary: #a5b7c9;
  --color-background-alternative: #1e4a78;

  /* Border Colors */
  --color-border: #d9d9d9;
  --color-border-primary: #d9d9d9;
  --color-border-secondary: #d9d9d9;
  --color-border-tertiary: #d9d9d9;
  --color-border-alternative: rgba(9, 22, 35, 0.15); /* Based on “Neutral Darkest 15” */

  /* Border Colors with Opacity */
  --color-border-50: rgba(217, 217, 217, 0.5); /* 50% opacity */
  --color-border-25: rgba(217, 217, 217, 0.25); /* 25% opacity */
  --color-border-75: rgba(217, 217, 217, 0.75); /* 75% opacity */

  /* Text Colors */
  --color-text: #000000; /* Neutral Darkest */
  --color-text-primary: #000000;
  --color-text-secondary: #000000;
  --color-text-alternative: #ffffff;

  /* Link Colors */
  --color-link: #1e4a78; /* Accent / Bay of Many */
  --color-link-primary: #183B5F;
  --color-link-secondary: #0C1D2F;
  --color-link-alternative: #62809F;

  --color-accent: #1e4a78;                     /* Used for buttons, highlights */

}