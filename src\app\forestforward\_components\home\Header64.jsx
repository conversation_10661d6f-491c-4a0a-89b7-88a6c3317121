"use client"

import { useState, useEffect } from "react"

const blockTitles = ["Design", "Code", "Create", "Build", "Launch", "Scale", "Test", "Deploy", "Monitor", "Iterate"]

const colors = [
  "bg-blue-400",
  "bg-blue-500",
  "bg-blue-600",
  "bg-indigo-400",
  "bg-indigo-500",
  "bg-cyan-400",
  "bg-cyan-500",
  "bg-sky-400",
  "bg-sky-500",
  "bg-slate-500",
]

const SPACING = 12 // Uniform spacing between blocks
const CONTAINER_WIDTH = 700
const CONTAINER_HEIGHT = 500

function canPlaceBlock(x, y, width, height, existingBlocks) {
  // Check bounds
  if (x < 0 || y < 0 || x + width > CONTAINER_WIDTH || y + height > CONTAINER_HEIGHT) {
    return false
  }

  // Check collision with existing blocks (including spacing)
  for (const block of existingBlocks) {
    const horizontalOverlap = x < block.x + block.width + SPACING && x + width + SPACING > block.x

    const verticalOverlap = y < block.y + block.height + SPACING && y + height + SPACING > block.y

    if (horizontalOverlap && verticalOverlap) {
      return false
    }
  }

  return true
}

function findBestPosition(width, height, existingBlocks) {
  const centerX = CONTAINER_WIDTH / 2
  const centerY = CONTAINER_HEIGHT / 2

  let bestX = 0
  let bestY = 0
  let bestScore = Number.POSITIVE_INFINITY

  // Try positions in a grid pattern for efficiency
  const step = 8

  for (let y = 0; y <= CONTAINER_HEIGHT - height; y += step) {
    for (let x = 0; x <= CONTAINER_WIDTH - width; x += step) {
      if (canPlaceBlock(x, y, width, height, existingBlocks)) {
        // Calculate center of the block
        const blockCenterX = x + width / 2
        const blockCenterY = y + height / 2

        // Score based on distance from container center (closer to center = better)
        const distanceFromCenter = Math.sqrt(Math.pow(blockCenterX - centerX, 2) + Math.pow(blockCenterY - centerY, 2))

        // Also consider proximity to existing blocks for Tetris-like fitting
        let proximityBonus = 0
        if (existingBlocks.length > 0) {
          const minDistanceToExisting = Math.min(
            ...existingBlocks.map((block) => {
              const existingCenterX = block.x + block.width / 2
              const existingCenterY = block.y + block.height / 2
              return Math.sqrt(
                Math.pow(blockCenterX - existingCenterX, 2) + Math.pow(blockCenterY - existingCenterY, 2),
              )
            }),
          )
          proximityBonus = -minDistanceToExisting * 0.1 // Bonus for being close to existing blocks
        }

        const score = distanceFromCenter + proximityBonus

        if (score < bestScore) {
          bestScore = score
          bestX = x
          bestY = y
        }
      }
    }
  }

  return { x: bestX, y: bestY }
}

function generateTetrisBlocks() {
  const blocks = []
  const usedSizes = new Set()

  // Generate unique random sizes
  const blockSizes = []

  while (blockSizes.length < blockTitles.length) {
    const width = Math.floor(Math.random() * 100) + 60 // 60-160px
    const height = Math.floor(Math.random() * 80) + 50 // 50-130px
    const sizeKey = `${width}x${height}`

    if (!usedSizes.has(sizeKey)) {
      usedSizes.add(sizeKey)
      blockSizes.push({ width, height })
    }
  }

  // Don't sort by size - keep random order for more organic center-based placement

  // Place first block near center
  if (blockSizes.length > 0) {
    const firstBlock = blockSizes[0]
    const centerX = (CONTAINER_WIDTH - firstBlock.width) / 2
    const centerY = (CONTAINER_HEIGHT - firstBlock.height) / 2

    blocks.push({
      id: 1,
      title: blockTitles[0],
      width: firstBlock.width,
      height: firstBlock.height,
      x: Math.round(centerX),
      y: Math.round(centerY),
      color: colors[0],
    })
  }

  // Place remaining blocks using center-based packing
  blockSizes.slice(1).forEach((size, index) => {
    const position = findBestPosition(size.width, size.height, blocks)

    blocks.push({
      id: index + 2,
      title: blockTitles[index + 1],
      width: size.width,
      height: size.height,
      x: position.x,
      y: position.y,
      color: colors[(index + 1) % colors.length],
    })
  })

  return blocks
}

export default function AbstractGrid() {
  const [blocks, setBlocks] = useState<Block[]>([])
  const [hoveredId, setHoveredId] = useState<number | null>(null)
  const [isGenerating, setIsGenerating] = useState(true)

  const generateLayout = async () => {
    setIsGenerating(true)
    await new Promise((resolve) => setTimeout(resolve, 200))
    setBlocks(generateTetrisBlocks())
    setIsGenerating(false)
  }

  useEffect(() => {
    generateLayout()
  }, [])

  return (
    <div className="w-full min-h-screen flex items-center justify-center p-8 bg-gray-50">
      <div className="w-full max-w-5xl mx-auto">
        <div className="mb-6 text-center">
          <button
            onClick={generateLayout}
            disabled={isGenerating}
            className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 font-medium"
          >
            {isGenerating ? "Generating Tetris Layout..." : "Generate New Tetris Layout"}
          </button>
        </div>

        <div className="flex justify-center">
          <div
            className="relative bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden"
            style={{
              width: CONTAINER_WIDTH,
              height: CONTAINER_HEIGHT,
            }}
          >
            {isGenerating ? (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-gray-500 text-lg">Creating Tetris-like layout...</div>
              </div>
            ) : (
              <>
                {blocks.map((block) => (
                  <div
                    key={block.id}
                    className={`
                      absolute
                      ${block.color}
                      rounded-lg
                      flex
                      items-center
                      justify-center
                      text-white
                      font-medium
                      text-sm
                      cursor-pointer
                      transition-all
                      duration-300
                      ease-out
                      ${hoveredId === block.id ? "shadow-xl -translate-y-1 z-10 scale-105" : "shadow-md"}
                    `}
                    style={{
                      left: `${block.x}px`,
                      top: `${block.y}px`,
                      width: `${block.width}px`,
                      height: `${block.height}px`,
                    }}
                    onMouseEnter={() => setHoveredId(block.id)}
                    onMouseLeave={() => setHoveredId(null)}
                  >
                    <span className="text-center px-3 font-semibold">{block.title}</span>
                  </div>
                ))}

                {/* Debug info */}
                <div className="absolute bottom-2 right-2 text-xs text-gray-400 bg-white/80 px-2 py-1 rounded">
                  {blocks.length} blocks • {SPACING}px spacing
                </div>
              </>
            )}
          </div>
        </div>

        <div className="mt-8 text-center space-y-2">
          <p className="text-sm text-gray-600 font-medium">
            Tetris-like packing • All blocks different sizes • No overlapping
          </p>
          <p className="text-xs text-gray-500">
            Blocks are packed tightly together like Tetris pieces with uniform {SPACING}px spacing
          </p>
        </div>
      </div>
    </div>
  )
}
