"use client";

import { But<PERSON> } from "@relume_io/relume-ui";
import React from "react";
import { RxChevronRight } from "react-icons/rx";

export function Layout233() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="grid grid-cols-1 items-start gap-y-12 md:grid-cols-3 md:gap-x-8 md:gap-y-16 lg:gap-x-12">
          <div>
            <h3 className="mb-5 text-2xl font-bold md:mb-6 md:text-3xl md:leading-[1.3] lg:text-4xl">
              Strategische communicatie
            </h3>
            <div className="mt-6 flex flex-wrap gap-4 md:mt-8">
              <Button iconRight={<RxChevronRight />} variant="link" size="link">
                <PERSON><PERSON> meer
              </Button>
            </div>
          </div>
          <div>
            <h3 className="mb-5 text-2xl font-bold md:mb-6 md:text-3xl md:leading-[1.3] lg:text-4xl">
              Storytelling
            </h3>
            <div className="mt-6 flex flex-wrap gap-4 md:mt-8">
              <Button iconRight={<RxChevronRight />} variant="link" size="link">
                Lees meer
              </Button>
            </div>
          </div>
          <div>
            <h3 className="mb-5 text-2xl font-bold md:mb-6 md:text-3xl md:leading-[1.3] lg:text-4xl">
              Mediarelaties
            </h3>
            <div className="mt-6 flex flex-wrap gap-4 md:mt-8">
              <Button iconRight={<RxChevronRight />} variant="link" size="link">
                Lees meer
              </Button>
            </div>
          </div>
          <div>
            <h3 className="mb-5 text-2xl font-bold md:mb-6 md:text-3xl md:leading-[1.3] lg:text-4xl">
              Trainingen & workshops
            </h3>
            <div className="mt-6 flex flex-wrap gap-4 md:mt-8">
              <Button iconRight={<RxChevronRight />} variant="link" size="link">
                Lees meer
              </Button>
            </div>
          </div>
          <div>
            <h3 className="mb-5 text-2xl font-bold md:mb-6 md:text-3xl md:leading-[1.3] lg:text-4xl">
              Design, branding & web development
            </h3>
            <div className="mt-6 flex flex-wrap gap-4 md:mt-8">
              <Button iconRight={<RxChevronRight />} variant="link" size="link">
                Lees meer
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
