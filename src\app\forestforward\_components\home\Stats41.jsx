"use client";

import { But<PERSON> } from "@relume_io/relume-ui";
import React, { Fragment } from "react";

export function Stats41() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="mb-12 grid grid-cols-1 gap-y-5 md:mb-18 md:grid-cols-2 md:gap-x-12 lg:mb-20 lg:gap-x-20">
          <div>
            <h2 className="text-5xl font-bold md:text-7xl lg:text-8xl">
              Visie
            </h2>
          </div>
          <div>
            <p className="md:text-md">
              Wij geloven in lokaal verduurzamen en vergroenen met een meetbaar
              verschil en langdurige betrokkenheid. Daarom combineren we
              natuurcreatie met beleving, educatie en wetenschappelijke
              opvolging. We hebben daarvoor het nodige netwerk en de juiste
              expertise in huis. Du<PERSON>, jij bepaalt welke tastbare impact jullie
              willen wil maken, wij regelen de rest.
            </p>
            <div className="mt-6 flex flex-wrap items-center gap-4 md:mt-8">
              <Button title="Ontdek onze visie en aanpak" variant="secondary" className="rounded-xl">
                Ontdek onze visie en aanpak
              </Button>
            </div>
          </div>
        </div>
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          <Fragment>
            <div className="p-8 first:flex first:flex-col first:md:col-span-2 first:md:row-span-1 first:lg:col-span-1 first:lg:row-span-2 [&:nth-last-child(2)]:order-last [&:nth-last-child(2)]:md:order-none">
              <h3 className="mb-8 text-md leading-[1.4] font-bold md:mb-10 md:text-xl lg:mb-12">
                Bedrijfsbossen
              </h3>
              <p className="text-right text-10xl leading-[1.3] font-bold md:text-[4rem] lg:text-[5rem] mt-auto">
                150ha
              </p>
              <div className="my-4 h-px w-full bg-border-primary" />
              <p className="text-right">Bedrijfsbossen geplant</p>
            </div>
          </Fragment>
          <Fragment>
            <div>
              <img
                className="aspect-[3/2] size-full rounded-image object-cover"
                src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image.svg"
                alt="Relume placeholder image"
              />
            </div>
          </Fragment>
          <Fragment>
            <div className="p-8 first:flex first:flex-col first:md:col-span-2 first:md:row-span-1 first:lg:col-span-1 first:lg:row-span-2 [&:nth-last-child(2)]:order-last [&:nth-last-child(2)]:md:order-none">
              <h3 className="mb-8 text-md leading-[1.4] font-bold md:mb-10 md:text-xl lg:mb-12">
                Bedrijfsbossen
              </h3>
              <p className="text-right text-10xl leading-[1.3] font-bold md:text-[4rem] lg:text-[5rem]">
                150ha
              </p>
              <div className="my-4 h-px w-full bg-border-primary" />
              <p className="text-right">Bedrijfsbossen geplant</p>
            </div>
          </Fragment>
          <Fragment>
            <div className="p-8 first:flex first:flex-col first:md:col-span-2 first:md:row-span-1 first:lg:col-span-1 first:lg:row-span-2 [&:nth-last-child(2)]:order-last [&:nth-last-child(2)]:md:order-none">
              <h3 className="mb-8 text-md leading-[1.4] font-bold md:mb-10 md:text-xl lg:mb-12">
                Bedrijfsbossen
              </h3>
              <p className="text-right text-10xl leading-[1.3] font-bold md:text-[4rem] lg:text-[5rem]">
                150ha
              </p>
              <div className="my-4 h-px w-full bg-border-primary" />
              <p className="text-right">Bedrijfsbossen geplant</p>
            </div>
          </Fragment>
          <Fragment>
            <div>
              <img
                className="aspect-[3/2] size-full rounded-image object-cover"
                src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image.svg"
                alt="Relume placeholder image"
              />
            </div>
          </Fragment>
        </div>
      </div>
    </section>
  );
}
