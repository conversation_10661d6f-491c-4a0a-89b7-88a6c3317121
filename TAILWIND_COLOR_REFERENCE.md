# Tailwind Color System Reference

This document provides a comprehensive reference for all available theme-aware Tailwind color classes in your project.

## Overview

Your color system uses CSS custom properties that are mapped to easy-to-read Tailwind classes. All themes currently use the same colors, making it easy to switch between them later.

## Available Color Classes

### Background Colors
Use these classes for background colors that adapt to the current theme:

- `bg-background` - Main background color (white)
- `bg-background-primary` - Primary background variant (very light gray)
- `bg-background-secondary` - Secondary background variant (light gray)
- `bg-background-tertiary` - Tertiary background variant (medium light gray)
- `bg-background-alternative` - Alternative background (dark)

**Static backgrounds (never change):**
- `bg-background-success` - Success background (light green)
- `bg-background-error` - Error background (light red)

### Border Colors
Use these classes for borders that adapt to the current theme:

- `border-border` - Default border color
- `border-border-primary` - Primary border variant
- `border-border-secondary` - Secondary border variant
- `border-border-tertiary` - Tertiary border variant
- `border-border-alternative` - Alternative border color

**Static borders (never change):**
- `border-border-success` - Success border (green)
- `border-border-error` - Error border (red)

### Text Colors
Use these classes for text colors that adapt to the current theme:

- `text-text` - Default text color (dark)
- `text-text-primary` - Primary text variant
- `text-text-secondary` - Secondary text variant (lighter)
- `text-text-alternative` - Alternative text color (white)

**Static text colors (never change):**
- `text-text-success` - Success text (green)
- `text-text-error` - Error text (red)

### Link Colors
Use these classes for links and interactive elements:

- `text-link` - Default link color (blue)
- `text-link-primary` - Primary link variant (darker blue)
- `text-link-secondary` - Secondary link variant (darkest blue)
- `text-link-alternative` - Alternative link color (lighter blue)

### Brand Colors (Static)
These colors never change across themes:

- `bg-brand-black` / `text-brand-black` - Pure black
- `bg-brand-white` / `text-brand-white` - Pure white

### Neutral Colors (Static)
These neutral grays never change across themes:

- `bg-neutral` / `text-neutral` - Default neutral gray
- `bg-neutral-black` / `text-neutral-black` - Neutral black
- `bg-neutral-white` / `text-neutral-white` - Neutral white
- `bg-neutral-lightest` / `text-neutral-lightest` - Lightest gray
- `bg-neutral-lighter` / `text-neutral-lighter` - Lighter gray
- `bg-neutral-light` / `text-neutral-light` - Light gray
- `bg-neutral-dark` / `text-neutral-dark` - Dark gray
- `bg-neutral-darker` / `text-neutral-darker` - Darker gray
- `bg-neutral-darkest` / `text-neutral-darkest` - Darkest gray

### System Colors (Static)
These system colors never change across themes:

- `bg-system-success-green` / `text-system-success-green` - Success green
- `bg-system-success-green-light` - Light success background
- `bg-system-error-red` / `text-system-error-red` - Error red
- `bg-system-error-red-light` - Light error background

## Usage Examples

### Basic Component
```jsx
function Card({ children }) {
  return (
    <div className="bg-background-primary border border-border rounded-lg p-6">
      <div className="text-text">
        {children}
      </div>
    </div>
  );
}
```

### Button Component
```jsx
function Button({ children, variant = "primary" }) {
  const baseClasses = "px-4 py-2 rounded-md font-medium transition-colors";
  
  const variants = {
    primary: "bg-link text-text-alternative hover:bg-link-primary",
    secondary: "bg-background-secondary text-text border border-border hover:bg-background-tertiary",
    outline: "border border-link text-link hover:bg-background-primary"
  };
  
  return (
    <button className={`${baseClasses} ${variants[variant]}`}>
      {children}
    </button>
  );
}
```

### Navigation Component
```jsx
function Navigation() {
  return (
    <nav className="bg-background border-b border-border">
      <div className="px-6 py-4">
        <a href="/" className="text-link hover:text-link-primary">
          Home
        </a>
      </div>
    </nav>
  );
}
```

## Theme Application

Themes are applied at the layout level using CSS classes:

- `.theme-homepage` - Default theme
- `.theme-forestforward` - Forest Forward theme
- `.theme-lagom` - Lagom theme  
- `.theme-storyforward` - Story Forward theme

## Best Practices

1. **Always use theme-aware classes** for colors that should change between themes
2. **Use static colors** for system states (success, error) and neutral elements
3. **Test components** in all themes to ensure they look good
4. **Use semantic naming** - the class names describe purpose, not appearance
5. **Maintain contrast** - ensure sufficient contrast in all themes

## Current Color Values

All themes currently use the same color palette:

- **Backgrounds**: White to light grays with dark alternative
- **Text**: Dark grays with white alternative
- **Links**: Blue color scheme
- **Borders**: Light to medium grays

This makes it easy to differentiate themes later by simply updating the CSS custom properties in each theme file.
