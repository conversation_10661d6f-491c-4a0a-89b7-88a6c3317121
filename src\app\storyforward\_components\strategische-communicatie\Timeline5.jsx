"use client";

import { But<PERSON> } from "@relume_io/relume-ui";
import { motion, useScroll, useTransform } from "framer-motion";
import React, { useRef } from "react";
import { RxChevronRight } from "react-icons/rx";

const Circle = () => {
  const circleRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: circleRef,
    offset: ["end end", "end center"],
  });
  const backgroundColor = {
    backgroundColor: useTransform(scrollYProgress, [0.85, 1], ["#ccc", "#000"]),
  };
  return (
    <div className="absolute -ml-8 flex h-full w-8 items-start justify-center">
      <motion.div
        ref={circleRef}
        style={backgroundColor}
        className="z-20 mt-7 size-[0.9375rem] rounded-full shadow-[0_0_0_8px_white] md:mt-8"
      />
    </div>
  );
};

export function Timeline5() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container max-w-lg">
        <div className="mb-12 md:mb-18 lg:mb-20">
          <div className="relative z-10 w-full max-w-lg">
            <h2 className="mb-5 text-5xl font-bold md:mb-6 md:text-7xl lg:text-8xl">
              TO DELETE
            </h2>
          </div>
        </div>
        <div className="grid w-full max-w-lg auto-cols-fr grid-cols-[max-content_1fr] items-start justify-items-center">
          <div className="relative left-0 flex h-full w-8 flex-col items-center md:left-auto">
            <div className="absolute z-10 h-16 w-1 bg-gradient-to-b from-background-primary to-transparent" />
            <div className="sticky top-0 mt-[-50vh] h-[50vh] w-[3px] bg-neutral-black" />
            <div className="h-full w-[3px] bg-neutral-lighter" />
            <div className="absolute bottom-0 z-0 h-16 w-1 bg-gradient-to-b from-transparent to-background-primary" />
            <div className="absolute top-[-50vh] h-[50vh] w-full bg-background-primary" />
          </div>
          <div className="grid auto-cols-fr grid-cols-1 gap-y-8 sm:gap-12 md:gap-20">
            <div className="relative">
              <Circle />
              <div className="mt-4 ml-4 flex flex-col md:ml-12">
                <h3 className="mb-3 text-4xl leading-[1.2] font-bold md:mb-4 md:text-5xl lg:text-6xl">
                  Stap 1 → De basis leggen
                </h3>
                <p>
                  Goed communiceren begint met een plan. Dat hoeft zeker geen
                  ellenlange paper te zijn. We houden van compact, maar het moet
                  vooral slim en doordacht zijn.
                </p>
                <div className="mt-6 flex flex-wrap items-center gap-4 md:mt-8">
                  <Button
                    variant="link"
                    size="link"
                    iconRight={<RxChevronRight />}
                  >
                    Contacteer ons
                  </Button>
                </div>
              </div>
            </div>
            <div className="relative">
              <Circle />
              <div className="mt-4 ml-4 flex flex-col md:ml-12">
                <h3 className="mb-3 text-4xl leading-[1.2] font-bold md:mb-4 md:text-5xl lg:text-6xl">
                  Stap 2 → Deep dive & kritische vragen
                </h3>
                <p>
                  Om je beter te leren kennen of om het project door te praten,
                  starten we bij voorkeur met een deep dive, waarbij we de
                  nodige (kritische) vragen niet uit de weg gaan. Doorheen de
                  jaren hebben we op dat vlak altijd gemerkt dat bedrijven
                  liever vooraf een kritische sparringpartner voor zich hebben,
                  dan achteraf met onverwachte vragen of situaties
                  geconfronteerd te worden.
                </p>
                <div className="mt-6 flex flex-wrap items-center gap-4 md:mt-8">
                  <Button
                    variant="link"
                    size="link"
                    iconRight={<RxChevronRight />}
                  >
                    Contacteer ons
                  </Button>
                </div>
              </div>
            </div>
            <div className="relative">
              <Circle />
              <div className="mt-4 ml-4 flex flex-col md:ml-12">
                <h3 className="mb-3 text-4xl leading-[1.2] font-bold md:mb-4 md:text-5xl lg:text-6xl">
                  Stap 3 → Sterk plan ontwikkelen
                </h3>
                <p>
                  Eens we je in’s en out’s kennen, gaan we voor een sterk plan,
                  dat jouw reputatie versterkt en stakeholders écht betrekt. We
                  vertalen jouw visie en inzichten naar een impactvol
                  communicatieplan dat aansluit bij je bedrijfsdoelen. We
                  definiëren samen de doelen, identificeren de doelgroepen,
                  benoemen de valkuilen en zetten er de juiste
                  communicatie-acties tegenover.
                </p>
                <div className="mt-6 flex flex-wrap items-center gap-4 md:mt-8">
                  <Button
                    variant="link"
                    size="link"
                    iconRight={<RxChevronRight />}
                  >
                    Contacteer ons
                  </Button>
                </div>
              </div>
            </div>
            <div className="relative">
              <Circle />
              <div className="mt-4 ml-4 flex flex-col md:ml-12">
                <h3 className="mb-3 text-4xl leading-[1.2] font-bold md:mb-4 md:text-5xl lg:text-6xl">
                  Stap 4 → Van strategie naar actie
                </h3>
                <p>
                  En dat laatste is niet onbelangrijk. Want eerlijk toegegeven,
                  we houden van een slimme strategie, maar nog meer van een
                  sterke uitvoering. Actie dus.
                </p>
                <div className="mt-6 flex flex-wrap items-center gap-4 md:mt-8">
                  <Button
                    variant="link"
                    size="link"
                    iconRight={<RxChevronRight />}
                  >
                    Contacteer ons
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
