"use client";

import React from "react";

export function Header19() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="grid grid-cols-1 gap-x-20 gap-y-12 md:gap-y-16 lg:grid-cols-2 lg:items-center">
          <div>
            <img
              src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image.svg"
              className="w-full rounded-image object-cover"
              alt="Relume placeholder image"
            />
          </div>
          <div>
            <h1 className="mb-5 text-6xl font-bold md:mb-6 md:text-9xl lg:text-10xl">
              Storytelling
            </h1>
            <p className="md:text-md">
              Bij Story Forward vertrekken we vanuit jouw verhaal. Afhankelijk
              daarvan kijken we naar de manier waarop we het vertellen. Onze
              experts stellen daarom de juiste vragen, denken mee, en creëren
              content die niet alleen scoort maar ook strategisch slim is. <PERSON>
              pakkende blogs of social mediaposts over nieuwswaardige
              persberichten tot magazines en rapporten die gelezen worden en
              video’s die bekeken worden: samen zorgen we ervoor dat jouw
              verhaal geloofwaardig wordt verteld. En niet onbelangrijk: door
              vanuit de content te vertrekken, kunnen we ook je efficiëntie
              verhogen. Want: één verhaal kan, mits de nodige voorbereiding,
              vaak op meerdere manieren verspreid worden.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
